"use client";

import React, { useState } from 'react';
import { drawingElementsStorage } from '@/stores/drawing-elements-store';
import {
  getDrawingElement,
  getElementLastUpdated,
  getAllDrawingElements,
  exportElementsWithMetadata,
  getMostRecentlyUpdatedElement
} from '@/utils/drawing-storage-utils';

/**
 * Demo component showing how to use drawing elements storage
 * This demonstrates storing elements with ID as key and lastUpdated timestamp
 */
export default function DrawingElementsDemo() {
  const [selectedElementId, setSelectedElementId] = useState('');
  const [elementInfo, setElementInfo] = useState<any>(null);

  // Get element by ID
  const handleGetElement = () => {
    if (!selectedElementId.trim()) return;

    const element = getDrawingElement(selectedElementId);
    const lastUpdated = getElementLastUpdated(selectedElementId);

    setElementInfo({
      id: selectedElementId,
      element,
      lastUpdated,
      exists: !!element
    });
  };

  // Get all elements
  const handleGetAllElements = () => {
    const allElements = getAllDrawingElements();
    const allWithMetadata = drawingElementsStorage.getAllElements();

    console.log('All elements (array):', allElements);
    console.log('All elements with metadata:', allWithMetadata);

    setElementInfo({
      type: 'all',
      count: allElements.length,
      elements: allElements,
      metadata: allWithMetadata
    });
  };

  // Get most recent element
  const handleGetMostRecent = () => {
    const mostRecent = getMostRecentlyUpdatedElement();
    setElementInfo({
      type: 'mostRecent',
      ...mostRecent
    });
  };

  // Export all elements with metadata
  const handleExport = () => {
    const exportData = exportElementsWithMetadata();
    console.log('Export data:', exportData);

    // You could download this as a file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `drawing-elements-${new Date().toISOString()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Clear all elements
  const handleClearAll = () => {
    if (confirm('Are you sure you want to clear all stored elements?')) {
      drawingElementsStorage.clearAllElements();
      setElementInfo(null);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Drawing Elements Storage Demo</h2>

      <div className="space-y-4">
        {/* Get element by ID */}
        <div className="border p-4 rounded">
          <h3 className="text-lg font-semibold mb-2">Get Element by ID</h3>
          <div className="flex gap-2">
            <input
              type="text"
              value={selectedElementId}
              onChange={(e) => setSelectedElementId(e.target.value)}
              placeholder="Enter element ID"
              className="border px-3 py-1 rounded flex-1"
            />
            <button
              onClick={handleGetElement}
              className="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Get Element
            </button>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={handleGetAllElements}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Get All Elements
          </button>

          <button
            onClick={handleGetMostRecent}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Get Most Recent
          </button>

          <button
            onClick={handleExport}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Export Elements
          </button>

          <button
            onClick={handleClearAll}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Clear All
          </button>
        </div>

        {/* Display element info */}
        {elementInfo && (
          <div className="border p-4 rounded bg-gray-50">
            <h3 className="text-lg font-semibold mb-2">Element Information</h3>
            <pre className="bg-white p-3 rounded border overflow-auto text-sm">
              {JSON.stringify(elementInfo, null, 2)}
            </pre>
          </div>
        )}

        {/* Usage instructions */}
        <div className="border p-4 rounded bg-blue-50">
          <h3 className="text-lg font-semibold mb-2">How it works:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li><strong>Individual Storage:</strong> Each drawing element is stored with its ID as the key</li>
            <li><strong>Timestamp:</strong> Every element includes a `lastUpdated` timestamp</li>
            <li><strong>Automatic Saving:</strong> Elements are automatically saved when you draw in Excalidraw</li>
            <li><strong>Persistent:</strong> Data persists in localStorage across browser sessions</li>
            <li><strong>Zustand Store:</strong> Uses Zustand for reactive state management</li>
          </ul>
        </div>

        {/* Storage structure example */}
        <div className="border p-4 rounded bg-yellow-50">
          <h3 className="text-lg font-semibold mb-2">Storage Structure:</h3>
          <pre className="bg-white p-3 rounded border text-sm">
{`{
  "element-id-1": {
    "element": { /* Excalidraw element data */ },
    "lastUpdated": "2024-01-15T10:30:00.000Z"
  },
  "element-id-2": {
    "element": { /* Excalidraw element data */ },
    "lastUpdated": "2024-01-15T10:31:00.000Z"
  }
}`}
          </pre>
        </div>
      </div>
    </div>
  );
}
