import { useDrawingElementsStore } from '@/stores/drawing-elements-store';
import { OrderedExcalidrawElement } from '@excalidraw/excalidraw/element/types';

/**
 * Utility functions for working with drawing elements storage
 * These functions can be used outside of React components
 */

// Get the store instance for use outside React components
export const getDrawingElementsStore = () => useDrawingElementsStore.getState();

/**
 * Save a single drawing element with timestamp
 */
export const saveDrawingElement = (id: string, element: OrderedExcalidrawElement) => {
  const store = getDrawingElementsStore();
  store.saveElement(id, element);
};

/**
 * Get a drawing element by ID
 */
export const getDrawingElement = (id: string): OrderedExcalidrawElement | null => {
  const store = getDrawingElementsStore();
  return store.getElement(id);
};

/**
 * Get the last updated timestamp for an element
 */
export const getElementLastUpdated = (id: string): string | null => {
  const store = getDrawingElementsStore();
  return store.getLastUpdated(id);
};

/**
 * Get element with its metadata (element + lastUpdated)
 */
export const getElementWithMetadata = (id: string) => {
  const store = getDrawingElementsStore();
  const element = store.getElement(id);
  const lastUpdated = store.getLastUpdated(id);
  
  return {
    element,
    lastUpdated,
    exists: !!element
  };
};

/**
 * Get all elements as an array
 */
export const getAllDrawingElements = (): OrderedExcalidrawElement[] => {
  const store = getDrawingElementsStore();
  return store.getElementsArray();
};

/**
 * Get all elements with their metadata
 */
export const getAllElementsWithMetadata = () => {
  const store = getDrawingElementsStore();
  return store.getAllElements();
};

/**
 * Check if an element exists in storage
 */
export const hasDrawingElement = (id: string): boolean => {
  const store = getDrawingElementsStore();
  return store.hasElement(id);
};

/**
 * Remove a specific element from storage
 */
export const removeDrawingElement = (id: string) => {
  const store = getDrawingElementsStore();
  store.removeElement(id);
};

/**
 * Clear all elements from storage
 */
export const clearAllDrawingElements = () => {
  const store = getDrawingElementsStore();
  store.clearAllElements();
};

/**
 * Save multiple elements at once
 */
export const saveMultipleDrawingElements = (elements: OrderedExcalidrawElement[]) => {
  const store = getDrawingElementsStore();
  store.saveElements(elements);
};

/**
 * Get elements that were updated after a specific timestamp
 */
export const getElementsUpdatedAfter = (timestamp: string): OrderedExcalidrawElement[] => {
  const store = getDrawingElementsStore();
  const allElements = store.getAllElements();
  
  return Object.values(allElements)
    .filter(data => data.lastUpdated > timestamp)
    .map(data => data.element);
};

/**
 * Get the most recently updated element
 */
export const getMostRecentlyUpdatedElement = (): { element: OrderedExcalidrawElement; lastUpdated: string } | null => {
  const store = getDrawingElementsStore();
  const allElements = store.getAllElements();
  
  if (Object.keys(allElements).length === 0) {
    return null;
  }
  
  const mostRecent = Object.values(allElements).reduce((latest, current) => {
    return current.lastUpdated > latest.lastUpdated ? current : latest;
  });
  
  return {
    element: mostRecent.element,
    lastUpdated: mostRecent.lastUpdated
  };
};

/**
 * Export elements to JSON with metadata
 */
export const exportElementsWithMetadata = () => {
  const store = getDrawingElementsStore();
  const allElements = store.getAllElements();
  
  return {
    exportedAt: new Date().toISOString(),
    elementsCount: Object.keys(allElements).length,
    elements: allElements
  };
};

/**
 * Import elements from JSON (with or without metadata)
 */
export const importElements = (data: any) => {
  const store = getDrawingElementsStore();
  
  if (Array.isArray(data)) {
    // If it's just an array of elements, save them
    store.saveElements(data);
  } else if (data.elements) {
    // If it's an export with metadata, restore the elements with their timestamps
    const elementsMap = data.elements;
    store.elements = elementsMap;
  }
};
