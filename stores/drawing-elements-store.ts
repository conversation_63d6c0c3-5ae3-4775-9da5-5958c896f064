import { OrderedExcalidrawElement } from '@excalidraw/excalidraw/element/types';

interface DrawingElementData {
  element: OrderedExcalidrawElement;
  lastUpdated: string;
}

// Simple localStorage-based storage without Zustand reactivity
class DrawingElementsStorage {
  private storageKey = 'drawing-elements-storage';

  private getStoredElements(): Record<string, DrawingElementData> {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  private setStoredElements(elements: Record<string, DrawingElementData>): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(elements));
    } catch (error) {
      console.error('Failed to save elements to localStorage:', error);
    }
  }

  saveElement(id: string, element: OrderedExcalidrawElement): void {
    const stored = this.getStoredElements();
    stored[id] = {
      element,
      lastUpdated: new Date().toISOString()
    };
    this.setStoredElements(stored);
  }

  saveElements(elements: OrderedExcalidrawElement[]): void {
    const timestamp = new Date().toISOString();
    const elementsMap: Record<string, DrawingElementData> = {};

    elements.forEach((element) => {
      elementsMap[element.id] = {
        element,
        lastUpdated: timestamp
      };
    });

    this.setStoredElements(elementsMap);
  }

  getElement(id: string): OrderedExcalidrawElement | null {
    const stored = this.getStoredElements();
    return stored[id]?.element || null;
  }

  getLastUpdated(id: string): string | null {
    const stored = this.getStoredElements();
    return stored[id]?.lastUpdated || null;
  }

  getElementsArray(): OrderedExcalidrawElement[] {
    const stored = this.getStoredElements();
    return Object.values(stored).map(data => data.element);
  }

  getAllElements(): Record<string, DrawingElementData> {
    return this.getStoredElements();
  }

  removeElement(id: string): void {
    const stored = this.getStoredElements();
    delete stored[id];
    this.setStoredElements(stored);
  }

  clearAllElements(): void {
    this.setStoredElements({});
  }

  hasElement(id: string): boolean {
    const stored = this.getStoredElements();
    return id in stored;
  }
}

// Create a singleton instance
export const drawingElementsStorage = new DrawingElementsStorage();
