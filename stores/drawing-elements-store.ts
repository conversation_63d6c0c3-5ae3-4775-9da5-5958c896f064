import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { OrderedExcalidrawElement } from '@excalidraw/excalidraw/element/types';

interface DrawingElementData {
  element: OrderedExcalidrawElement;
  lastUpdated: string;
}

interface DrawingElementsStore {
  elements: Record<string, DrawingElementData>;
  saveElement: (id: string, element: OrderedExcalidrawElement) => void;
  saveElements: (elements: OrderedExcalidrawElement[]) => void;
  getElement: (id: string) => OrderedExcalidrawElement | null;
  getLastUpdated: (id: string) => string | null;
  removeElement: (id: string) => void;
  clearAllElements: () => void;
  getAllElements: () => Record<string, DrawingElementData>;
  getElementsArray: () => OrderedExcalidrawElement[];
  hasElement: (id: string) => boolean;
}

export const useDrawingElementsStore = create<DrawingElementsStore>()(
  persist(
    (set, get) => ({
      elements: {},
      
      saveElement: (id: string, element: OrderedExcalidrawElement) => {
        set((state) => ({
          elements: {
            ...state.elements,
            [id]: {
              element,
              lastUpdated: new Date().toISOString()
            }
          }
        }));
      },
      
      saveElements: (elements: OrderedExcalidrawElement[]) => {
        const timestamp = new Date().toISOString();
        const elementsMap: Record<string, DrawingElementData> = {};
        
        elements.forEach((element) => {
          elementsMap[element.id] = {
            element,
            lastUpdated: timestamp
          };
        });
        
        set({ elements: elementsMap });
      },
      
      getElement: (id: string) => {
        const elementData = get().elements[id];
        return elementData?.element || null;
      },
      
      getLastUpdated: (id: string) => {
        const elementData = get().elements[id];
        return elementData?.lastUpdated || null;
      },
      
      removeElement: (id: string) => {
        set((state) => {
          const { [id]: removed, ...rest } = state.elements;
          return { elements: rest };
        });
      },
      
      clearAllElements: () => {
        set({ elements: {} });
      },
      
      getAllElements: () => {
        return get().elements;
      },
      
      getElementsArray: () => {
        const elementsMap = get().elements;
        return Object.values(elementsMap).map(data => data.element);
      },
      
      hasElement: (id: string) => {
        return id in get().elements;
      }
    }),
    {
      name: 'drawing-elements-storage', // localStorage key
      // Optional: customize what gets persisted
      partialize: (state) => ({ elements: state.elements }),
    }
  )
);
